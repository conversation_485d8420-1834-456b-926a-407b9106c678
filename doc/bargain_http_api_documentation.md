# 助力砍价HTTP接口文档

## 概述

本文档描述了助力砍价功能的HTTP接口，面向APP端用户提供完整的砍价营销功能。

## 基础信息

- **基础路径**: `/api/bargain`
- **请求格式**: JSON
- **响应格式**: JSON
- **字符编码**: UTF-8
- **用户认证**: 所有接口都需要在请求头中传递 `userId`，由网关统一处理用户认证后设置

## 统一响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1640995200000
}
```

- `code`: 响应码，200表示成功，其他表示失败
- `message`: 响应消息
- `data`: 响应数据
- `timestamp`: 时间戳

## 接口列表

### 1. 发起助力活动

**接口地址**: `POST /api/bargain/start`

**请求头**:
```
userId: user001
```

**请求参数**:
```json
{
  "activityId": 1,
  "appId": "app001",
  "productId": "product001"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "发起助力活动成功",
  "data": {
    "id": 123456789,
    "activityId": 1,
    "userId": "user001",
    "appId": "app001",
    "productId": "product001",
    "originalPrice": 9900,
    "floorPrice": 2990,
    "currentPrice": 9900,
    "totalBargainAmount": 0,
    "assistCount": 0,
    "minAssistCount": 10,
    "status": 1,
    "statusDesc": "进行中",
    "canPurchase": false,
    "remainingAssistCount": 10,
    "remainingBargainAmount": 6910,
    "progressPercent": 0,
    "expireTime": "2024-01-01T23:59:59",
    "createTime": "2024-01-01T10:00:00"
  }
}
```

### 2. 参与助力

**接口地址**: `POST /api/bargain/assist`

**请求头**:
```
userId: user002
```

**请求参数**:
```json
{
  "userBargainId": 123456789,
  "appId": "app001"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "助力成功",
  "data": {
    "id": 987654321,
    "userBargainId": 123456789,
    "assistUserId": "user002",
    "initiatorUserId": "user001",
    "bargainAmount": 520,
    "assistOrder": 1,
    "appId": "app001",
    "createTime": "2024-01-01T10:30:00",
    "bargainAmountText": "砍掉了5.20元"
  }
}
```

### 3. 查询用户当前进行中的助力活动

**接口地址**: `GET /api/bargain/current-activity`

**请求头**:
```
userId: user001
```

**请求参数**:
- `appId` (必填): 应用ID
- `includeRecords` (可选): 是否包含助力记录，默认true

**请求示例**:
```
GET /api/bargain/current-activity?appId=app001&includeRecords=true
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 123456789,
    "activityId": 1,
    "userId": "user001",
    "productId": "product001",
    "originalPrice": 9900,
    "currentPrice": 8900,
    "status": 1,
    "statusDesc": "进行中",
    "progressPercent": 15,
    "expireTime": "2024-01-01T23:59:59",
    "assistRecords": [
      {
        "id": 987654321,
        "assistUserId": "user002",
        "bargainAmount": 520,
        "assistOrder": 1,
        "createTime": "2024-01-01T10:30:00",
        "bargainAmountText": "砍掉了5.20元"
      }
    ]
  }
}
```

**无活动时响应**:
```json
{
  "code": 200,
  "message": "暂无进行中的助力活动",
  "data": null
}
```

### 4. 查询助力活动详情

**接口地址**: `GET /api/bargain/activity/{userBargainId}`

**路径参数**:
- `userBargainId`: 助力活动ID

**查询参数**:
- `includeRecords` (可选): 是否包含助力记录，默认true

**请求示例**:
```
GET /api/bargain/activity/123456789?includeRecords=true
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 123456789,
    "activityId": 1,
    "userId": "user001",
    "productId": "product001",
    "originalPrice": 9900,
    "floorPrice": 2990,
    "currentPrice": 8900,
    "totalBargainAmount": 1000,
    "assistCount": 2,
    "minAssistCount": 10,
    "status": 1,
    "statusDesc": "进行中",
    "canPurchase": false,
    "remainingAssistCount": 8,
    "remainingBargainAmount": 5910,
    "progressPercent": 15,
    "assistRecords": [
      {
        "id": 987654321,
        "assistUserId": "user002",
        "bargainAmount": 520,
        "assistOrder": 1,
        "createTime": "2024-01-01T10:30:00",
        "bargainAmountText": "砍掉了5.20元"
      },
      {
        "id": 987654322,
        "assistUserId": "user003",
        "bargainAmount": 480,
        "assistOrder": 2,
        "createTime": "2024-01-01T11:00:00",
        "bargainAmountText": "砍掉了4.80元"
      }
    ]
  }
}
```

### 5. 检查用户是否可以助力

**接口地址**: `GET /api/bargain/check-assist`

**请求头**:
```
userId: user002
```

**请求参数**:
- `userBargainId` (必填): 助力活动ID
- `appId` (必填): 应用ID

**请求示例**:
```
GET /api/bargain/check-assist?userBargainId=123456789&appId=app001
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "canAssist": true,
    "reason": "可以助力"
  }
}
```

### 6. 获取用户今日剩余助力次数

**接口地址**: `GET /api/bargain/remaining-assist-count`

**请求参数**:
- `userId` (必填): 用户ID
- `appId` (必填): 应用ID

**请求示例**:
```
GET /api/bargain/remaining-assist-count?userId=user001&appId=app001
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": 3
}
```

## 错误码说明

- `200`: 成功
- `400`: 业务异常
- `422`: 参数校验失败
- `500`: 系统内部错误

## 状态说明

- `1`: 进行中
- `2`: 成功(可购买)
- `3`: 失败
- `4`: 已购买

## 注意事项

1. 所有金额单位为分
2. 时间格式为ISO 8601标准格式
3. 用户每天最多助力5次
4. 同一用户对同一发起人只能助力一次
5. 不能助力自己发起的活动
