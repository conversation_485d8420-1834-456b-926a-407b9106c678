package com.blsc.marketing.bargain.service;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 随机助力人数功能测试
 */
@DisplayName("随机助力人数功能测试")
class RandomAssistCountTest {

    /**
     * 生成随机助力人数的静态方法（从BargainActivityServiceImpl复制过来用于测试）
     */
    private static int generateRandomAssistCount(int minAssistCount, int maxAssistCount) {
        if (minAssistCount >= maxAssistCount) {
            return minAssistCount;
        }

        // 使用当前时间戳作为随机种子，确保每次调用都有不同的随机性
        Random random = new Random(System.nanoTime());

        // 在 [minAssistCount, maxAssistCount] 范围内随机生成
        return minAssistCount + random.nextInt(maxAssistCount - minAssistCount + 1);
    }

    @Test
    @DisplayName("测试随机助力人数生成 - 基本功能")
    void testGenerateRandomAssistCount() {
        int minAssistCount = 10;
        int maxAssistCount = 20;

        // 测试多次生成，验证随机性和范围
        for (int i = 0; i < 100; i++) {
            int result = generateRandomAssistCount(minAssistCount, maxAssistCount);

            // 验证结果在范围内
            assertTrue(result >= minAssistCount,
                    String.format("生成的助力人数%d应该大于等于最小值%d", result, minAssistCount));
            assertTrue(result <= maxAssistCount,
                    String.format("生成的助力人数%d应该小于等于最大值%d", result, maxAssistCount));
        }

        System.out.println("✅ 随机助力人数生成范围验证通过");
    }

    @Test
    @DisplayName("测试随机助力人数生成 - 随机性验证")
    void testGenerateRandomAssistCountRandomness() {
        int minAssistCount = 5;
        int maxAssistCount = 15;

        // 统计生成的数字分布
        Map<Integer, Integer> distribution = new HashMap<>();
        int testCount = 1000;

        for (int i = 0; i < testCount; i++) {
            int result = generateRandomAssistCount(minAssistCount, maxAssistCount);
            distribution.put(result, distribution.getOrDefault(result, 0) + 1);
        }
        
        System.out.println("=== 随机助力人数分布统计 ===");
        for (int i = minAssistCount; i <= maxAssistCount; i++) {
            int count = distribution.getOrDefault(i, 0);
            double percentage = (double) count / testCount * 100;
            System.out.printf("%d人: %d次 (%.1f%%)%n", i, count, percentage);
        }
        
        // 验证随机性：每个可能的值都应该出现
        for (int i = minAssistCount; i <= maxAssistCount; i++) {
            assertTrue(distribution.containsKey(i), 
                    String.format("助力人数%d应该在随机生成中出现", i));
            assertTrue(distribution.get(i) > 0, 
                    String.format("助力人数%d的出现次数应该大于0", i));
        }
        
        // 验证分布的均匀性（简单检查：每个值的出现次数不应该相差太大）
        int expectedCount = testCount / (maxAssistCount - minAssistCount + 1);
        for (int count : distribution.values()) {
            // 允许30%的偏差
            assertTrue(Math.abs(count - expectedCount) <= expectedCount * 0.3, 
                    String.format("分布应该相对均匀，实际次数%d与期望次数%d偏差不应过大", count, expectedCount));
        }
        
        System.out.println("✅ 随机助力人数随机性验证通过");
    }

    @Test
    @DisplayName("测试随机助力人数生成 - 边界条件")
    void testGenerateRandomAssistCountBoundary() {
        // 测试最小值等于最大值的情况
        int result1 = generateRandomAssistCount(10, 10);
        assertEquals(10, result1, "当最小值等于最大值时，应该返回该值");

        // 测试最小值大于最大值的情况（异常情况）
        int result2 = generateRandomAssistCount(15, 10);
        assertEquals(15, result2, "当最小值大于最大值时，应该返回最小值");

        // 测试最小差值的情况
        for (int i = 0; i < 50; i++) {
            int result3 = generateRandomAssistCount(5, 6);
            assertTrue(result3 == 5 || result3 == 6, "结果应该是5或6");
        }
        
        System.out.println("✅ 随机助力人数边界条件验证通过");
    }

    @Test
    @DisplayName("测试随机助力人数生成 - 实际业务场景")
    void testGenerateRandomAssistCountBusinessScenario() {
        // 模拟实际业务场景：10-20人的助力活动
        int minAssistCount = 10;
        int maxAssistCount = 20;

        System.out.println("=== 模拟用户发起助力活动 ===");

        // 模拟10个用户发起助力活动
        for (int userId = 1; userId <= 10; userId++) {
            int requiredCount = generateRandomAssistCount(minAssistCount, maxAssistCount);
            System.out.printf("用户%d发起助力活动，所需助力人数: %d人%n", userId, requiredCount);

            // 验证范围
            assertTrue(requiredCount >= minAssistCount && requiredCount <= maxAssistCount,
                    String.format("用户%d的助力人数%d应该在范围[%d, %d]内", userId, requiredCount, minAssistCount, maxAssistCount));
        }
        
        System.out.println("✅ 业务场景验证通过");
    }

    @Test
    @DisplayName("测试随机助力人数生成 - 不同配置场景")
    void testGenerateRandomAssistCountDifferentConfigs() {
        // 测试不同的配置场景
        int[][] configs = {
            {5, 10},   // 小范围
            {10, 20},  // 中等范围
            {15, 30},  // 大范围
            {1, 5},    // 很小范围
            {20, 50}   // 很大范围
        };

        System.out.println("=== 不同配置场景测试 ===");

        for (int[] config : configs) {
            int min = config[0];
            int max = config[1];

            System.out.printf("配置: [%d, %d] -> ", min, max);

            // 生成5个样本
            for (int i = 0; i < 5; i++) {
                int result = generateRandomAssistCount(min, max);
                System.out.printf("%d ", result);

                // 验证范围
                assertTrue(result >= min && result <= max,
                        String.format("结果%d应该在范围[%d, %d]内", result, min, max));
            }
            System.out.println();
        }
        
        System.out.println("✅ 不同配置场景验证通过");
    }
}
