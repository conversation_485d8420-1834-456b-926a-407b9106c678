package com.blsc.marketing.bargain.bean.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 助力活动响应
 */
@Data
public class BargainActivityResponse {
    
    /**
     * ID
     */
    private Long id;
    
    /**
     * 活动配置ID
     */
    private Long activityId;
    
    /**
     * 发起用户ID
     */
    private String userId;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 商品ID
     */
    private String productId;
    
    /**
     * 商品原价(分)
     */
    private Integer originalPrice;
    
    /**
     * 底价金额(分)
     */
    private Integer floorPrice;
    
    /**
     * 当前价格(分)
     */
    private Integer currentPrice;
    
    /**
     * 已砍价总金额(分)
     */
    private Integer totalBargainAmount;
    
    /**
     * 当前助力人数
     */
    private Integer assistCount;
    
    /**
     * 最少助力人数阈值
     */
    private Integer minAssistCount;
    
    /**
     * 状态：1-进行中，2-成功(可购买)，3-失败，4-已购买
     */
    private Integer status;
    
    /**
     * 状态描述
     */
    private String statusDesc;
    
    /**
     * 活动过期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 成功解锁时间
     */
    private LocalDateTime successTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 助力记录列表
     */
    private List<BargainAssistRecordResponse> assistRecords;
    
    /**
     * 是否可以购买
     */
    private Boolean canPurchase;
    
    /**
     * 距离解锁还需要的助力人数
     */
    private Integer remainingAssistCount;
    
    /**
     * 距离解锁还需要的砍价金额
     */
    private Integer remainingBargainAmount;
    
    /**
     * 砍价进度百分比(0-100)
     */
    private Integer progressPercent;
}
