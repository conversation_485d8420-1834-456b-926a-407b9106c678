package com.blsc.marketing.bargain.util;

import com.blsc.marketing.bargain.bean.PageData;
import com.blsc.marketing.bargain.bean.bo.BargainAssistRecordBO;
import com.blsc.marketing.bargain.bean.bo.UserBargainActivityBO;
import com.blsc.marketing.bargain.bean.dto.AssistBargainDTO;
import com.blsc.marketing.bargain.bean.dto.QueryBargainActivityDTO;
import com.blsc.marketing.bargain.bean.dto.StartBargainActivityDTO;
import com.blsc.marketing.bargain.bean.vo.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 助力砍价VO转换工具类
 */
public class BargainVOConverter {
    
    /**
     * 请求VO转DTO
     */
    public static StartBargainActivityDTO convertToDTO(StartBargainActivityRequest request) {
        if (request == null) {
            return null;
        }
        
        StartBargainActivityDTO dto = new StartBargainActivityDTO();
        dto.setActivityId(request.getActivityId());
        dto.setUserId(request.getUserId());
        dto.setAppId(request.getAppId());
        dto.setProductId(request.getProductId());
        return dto;
    }
    
    /**
     * 请求VO转DTO
     */
    public static AssistBargainDTO convertToDTO(AssistBargainRequest request) {
        if (request == null) {
            return null;
        }
        
        AssistBargainDTO dto = new AssistBargainDTO();
        dto.setUserBargainId(request.getUserBargainId());
        dto.setAssistUserId(request.getAssistUserId());
        dto.setAppId(request.getAppId());
        return dto;
    }
    
    /**
     * 请求VO转DTO
     */
    public static QueryBargainActivityDTO convertToDTO(QueryBargainActivityRequest request) {
        if (request == null) {
            return null;
        }
        
        QueryBargainActivityDTO dto = new QueryBargainActivityDTO();
        dto.setUserId(request.getUserId());
        dto.setAppId(request.getAppId());
        dto.setStatus(request.getStatus());
        dto.setPageNum(request.getPageNum());
        dto.setPageSize(request.getPageSize());
        return dto;
    }
    
    /**
     * BO转响应VO
     */
    public static BargainActivityResponse convertToResponse(UserBargainActivityBO bo) {
        if (bo == null) {
            return null;
        }
        
        BargainActivityResponse response = new BargainActivityResponse();
        response.setId(bo.getId());
        response.setActivityId(bo.getActivityId());
        response.setUserId(bo.getUserId());
        response.setAppId(bo.getAppId());
        response.setProductId(bo.getProductId());
        response.setOriginalPrice(bo.getOriginalPrice());
        response.setFloorPrice(bo.getFloorPrice());
        response.setCurrentPrice(bo.getCurrentPrice());
        response.setTotalBargainAmount(bo.getTotalBargainAmount());
        response.setAssistCount(bo.getAssistCount());
        response.setMinAssistCount(bo.getMinAssistCount());
        response.setStatus(bo.getStatus());
        response.setStatusDesc(getStatusDesc(bo.getStatus()));
        response.setExpireTime(bo.getExpireTime());
        response.setSuccessTime(bo.getSuccessTime());
        response.setCreateTime(bo.getCreateTime());
        response.setCanPurchase(bo.getCanPurchase());
        response.setRemainingAssistCount(bo.getRemainingAssistCount());
        response.setRemainingBargainAmount(bo.getRemainingBargainAmount());
        
        // 计算砍价进度百分比
        if (bo.getOriginalPrice() != null && bo.getFloorPrice() != null && bo.getCurrentPrice() != null) {
            int maxBargainAmount = bo.getOriginalPrice() - bo.getFloorPrice();
            int currentBargainAmount = bo.getOriginalPrice() - bo.getCurrentPrice();
            if (maxBargainAmount > 0) {
                response.setProgressPercent((int) Math.round((double) currentBargainAmount / maxBargainAmount * 100));
            } else {
                response.setProgressPercent(0);
            }
        }
        
        // 转换助力记录
        if (bo.getAssistRecords() != null) {
            response.setAssistRecords(bo.getAssistRecords().stream()
                    .map(BargainVOConverter::convertToResponse)
                    .collect(Collectors.toList()));
        }
        
        return response;
    }
    
    /**
     * BO转响应VO
     */
    public static BargainAssistRecordResponse convertToResponse(BargainAssistRecordBO bo) {
        if (bo == null) {
            return null;
        }
        
        BargainAssistRecordResponse response = new BargainAssistRecordResponse();
        response.setId(bo.getId());
        response.setUserBargainId(bo.getUserBargainId());
        response.setAssistUserId(bo.getAssistUserId());
        response.setInitiatorUserId(bo.getInitiatorUserId());
        response.setBargainAmount(bo.getBargainAmount());
        response.setAssistOrder(bo.getAssistOrder());
        response.setAppId(bo.getAppId());
        response.setCreateTime(bo.getCreateTime());
        response.setAssistUserNickname(bo.getAssistUserNickname());
        response.setAssistUserAvatar(bo.getAssistUserAvatar());
        
        // 生成砍价金额显示文本
        if (bo.getBargainAmount() != null) {
            BigDecimal amount = new BigDecimal(bo.getBargainAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            response.setBargainAmountText("砍掉了" + amount + "元");
        }
        
        return response;
    }
    
    /**
     * 分页数据转换
     */
    public static <T, R> PageResponse<R> convertToPageResponse(PageData<T> pageData, 
                                                               List<R> convertedList) {
        if (pageData == null) {
            return null;
        }
        
        return new PageResponse<>(
                convertedList,
                pageData.getTotal(),
                pageData.getPageNum(),
                pageData.getPageSize()
        );
    }
    
    /**
     * 获取状态描述
     */
    private static String getStatusDesc(Integer status) {
        if (status == null) {
            return "未知";
        }
        
        return switch (status) {
            case 1 -> "进行中";
            case 2 -> "成功";
            case 3 -> "失败";
            case 4 -> "已购买";
            default -> "未知";
        };
    }
}
