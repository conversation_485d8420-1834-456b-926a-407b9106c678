package com.blsc.marketing.bargain.bean.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 助力记录响应
 */
@Data
public class BargainAssistRecordResponse {
    
    /**
     * ID
     */
    private Long id;
    
    /**
     * 用户助力活动ID
     */
    private Long userBargainId;
    
    /**
     * 助力用户ID
     */
    private String assistUserId;
    
    /**
     * 发起用户ID
     */
    private String initiatorUserId;
    
    /**
     * 本次砍价金额(分)
     */
    private Integer bargainAmount;
    
    /**
     * 助力顺序(第几个助力)
     */
    private Integer assistOrder;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 助力用户昵称(可选，用于展示)
     */
    private String assistUserNickname;
    
    /**
     * 助力用户头像(可选，用于展示)
     */
    private String assistUserAvatar;
    
    /**
     * 砍价金额显示文本(如: "砍掉了5.20元")
     */
    private String bargainAmountText;
}
