package com.blsc.marketing.bargain.bean;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName JsonResult
 * @Description 统一返回对象
 * <AUTHOR>
 * @Date 2024/10/28
 */
@Data
public class JsonResult<T> implements Serializable {

    public static final Integer SUCCESS_CODE = 0;

    /**
     * 操作结果的状态码（状态标识）
     */
    private Integer code;

    /**
     * 操作失败时的提示文本
     */
    private String message;

    /**
     * 操作成功时响应的数据
     */
    private T data;

    /**
     * 生成表示"成功"的响应对象
     *
     * @return 表示"成功"的响应对象
     */
    public static JsonResult<Void> ok() {
        return ok(null);
    }

    /**
     * 生成表示"成功"的响应对象，此对象中将包含响应到客户端的数据
     *
     * @param data 响应到客户端的数据
     * @return 表示"成功"的响应对象
     */
    public static <T> JsonResult<T> ok(T data) {
        JsonResult<T> jsonResult = new JsonResult<>();
        jsonResult.setCode(SUCCESS_CODE);
        jsonResult.setData(data);
        return jsonResult;
    }

    /**
     * 手动指定 code 和 data 生成响应对象
     * @param code 状态码
     * @param data 响应数据
     * @return 表示"成功"的响应对象
     */
    public static <T> JsonResult<T> ok(Integer code, T data) {
        JsonResult<T> jsonResult = new JsonResult<>();
        jsonResult.setCode(code);
        jsonResult.setData(data);
        return jsonResult;
    }

    /**
     * 生成表示"失败"的响应对象，此对象中将包含提示文本信息
     *
     * @param message 提示文本
     * @return 表示"失败"的响应对象
     */
    public static JsonResult<Void> fail(String message) {
        JsonResult<Void> jsonResult = new JsonResult<>();
        jsonResult.setMessage(message);
        return jsonResult;
    }

    /**
     * 生成表示"失败"的响应对象，包含业务状态码和提示文本信息
     *
     * @param code    业务状态码
     * @param message 提示文本
     * @return 表示"失败"的响应对象
     */
    public static JsonResult<Void> fail(Integer code, String message) {
        JsonResult<Void> jsonResult = new JsonResult<>();
        jsonResult.setCode(code);
        jsonResult.setMessage(message);
        return jsonResult;
    }

}
