package com.blsc.marketing.bargain.controller;

import com.blsc.marketing.bargain.bean.JsonResult;
import com.blsc.marketing.bargain.bean.PageData;
import com.blsc.marketing.bargain.bean.bo.BargainAssistRecordBO;
import com.blsc.marketing.bargain.bean.bo.UserBargainActivityBO;
import com.blsc.marketing.bargain.bean.vo.*;
import com.blsc.marketing.bargain.service.BargainActivityService;
import com.blsc.marketing.bargain.util.BargainVOConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 助力砍价HTTP控制器
 */
@RestController
@RequestMapping("/app/api/v1/bargain")
@Validated
@Slf4j
public class BargainActivityController {

    private final BargainActivityService bargainActivityService;

    public BargainActivityController(BargainActivityService bargainActivityService) {
        this.bargainActivityService = bargainActivityService;
    }

    /**
     * 发起助力活动
     */
    @PostMapping("/start")
    public JsonResult<BargainActivityResponse> startBargainActivity(
            @RequestHeader("userId") @NotBlank(message = "用户ID不能为空") String userId,
            @Valid @RequestBody StartBargainActivityRequest request) {

        log.info("用户发起助力活动: userId={}, activityId={}, productId={}",
                userId, request.getActivityId(), request.getProductId());

        request.setUserId(userId);

        UserBargainActivityBO result = bargainActivityService.startBargainActivity(
                BargainVOConverter.convertToDTO(request));

        BargainActivityResponse response = BargainVOConverter.convertToResponse(result);

        return JsonResult.ok(response);
    }

    /**
     * 参与助力
     */
    @PostMapping("/assist")
    public JsonResult<BargainAssistRecordResponse> assistBargain(
            @RequestHeader("userId") @NotBlank(message = "用户ID不能为空") String assistUserId,
            @Valid @RequestBody AssistBargainRequest request) {

        log.info("用户参与助力: assistUserId={}, userBargainId={}",
                assistUserId, request.getUserBargainId());

        request.setAssistUserId(assistUserId);

        BargainAssistRecordBO result = bargainActivityService.assistBargain(
                BargainVOConverter.convertToDTO(request));

        BargainAssistRecordResponse response = BargainVOConverter.convertToResponse(result);

        return JsonResult.ok(response);
    }

    /**
     * 查询用户当前进行中的助力活动
     */
    @GetMapping("/current-activity")
    public JsonResult<BargainActivityResponse> getCurrentBargainActivity(
            @RequestHeader("userId") @NotBlank(message = "用户ID不能为空") String userId) {

        log.debug("查询用户当前进行中的助力活动: userId={}", userId);

        QueryBargainActivityRequest request = new QueryBargainActivityRequest();
        request.setUserId(userId);
//        request.setAppId(appId);
        request.setStatus(1); // 只查询进行中的活动
        request.setPageNum(1);
        request.setPageSize(1);

        PageData<UserBargainActivityBO> pageData = bargainActivityService.queryUserBargainActivities(
                BargainVOConverter.convertToDTO(request));

        if (pageData.getList().isEmpty()) {
            return JsonResult.ok(null);
        }

        UserBargainActivityBO activity = pageData.getList().getFirst();

        BargainActivityResponse response = BargainVOConverter.convertToResponse(activity);

        return JsonResult.ok(response);
    }

    /**
     * 根据ID查询助力活动详情
     */
    @GetMapping("/activity/{userBargainId}")
    public JsonResult<BargainActivityResponse> getBargainActivityById(
            @PathVariable @NotNull(message = "助力活动ID不能为空") Long userBargainId,
            @RequestParam(defaultValue = "true") Boolean includeRecords) {
        
        log.info("查询助力活动详情: userBargainId={}, includeRecords={}", 
                userBargainId, includeRecords);
        
        UserBargainActivityBO result = bargainActivityService.getBargainActivityById(
                userBargainId, includeRecords);
        
        BargainActivityResponse response = BargainVOConverter.convertToResponse(result);
        
        return JsonResult.ok(response);
    }

    /**
     * 检查用户是否可以助力
     */
    @GetMapping("/check-assist")
    public JsonResult<BargainAssistCheckResponse> checkCanAssist(
            @RequestHeader("userId") @NotBlank(message = "用户ID不能为空") String assistUserId,
            @RequestParam @NotNull(message = "助力活动ID不能为空") Long userBargainId,
            @RequestParam @NotBlank(message = "应用ID不能为空") String appId) {

        log.info("检查用户是否可以助力: assistUserId={}, userBargainId={}",
                assistUserId, userBargainId);

        BargainActivityService.BargainAssistCheckResult result =
                bargainActivityService.checkCanAssist(assistUserId, userBargainId, appId);

        BargainAssistCheckResponse response = new BargainAssistCheckResponse();
        response.setCanAssist(result.isCanAssist());
        response.setReason(result.getReason());

        return JsonResult.ok(response);
    }

    /**
     * 获取用户今日剩余助力次数
     */
    @GetMapping("/remaining-assist-count")
    public JsonResult<Integer> getUserDailyRemainingAssistCount(
            @RequestHeader("userId") @NotBlank(message = "用户ID不能为空") String userId,
            @RequestParam @NotBlank(message = "应用ID不能为空") String appId) {

        log.info("获取用户今日剩余助力次数: userId={}", userId);

        Integer remainingCount = bargainActivityService.getUserDailyRemainingAssistCount(userId, appId);

        return JsonResult.ok(remainingCount);
    }
}
