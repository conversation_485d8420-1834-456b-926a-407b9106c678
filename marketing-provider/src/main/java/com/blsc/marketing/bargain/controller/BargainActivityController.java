package com.blsc.marketing.bargain.controller;

import com.blsc.marketing.bargain.bean.PageData;
import com.blsc.marketing.bargain.bean.bo.BargainAssistRecordBO;
import com.blsc.marketing.bargain.bean.bo.UserBargainActivityBO;
import com.blsc.marketing.bargain.bean.vo.*;
import com.blsc.marketing.bargain.service.BargainActivityService;
import com.blsc.marketing.bargain.util.BargainVOConverter;
import com.blsc.marketing.common.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 助力砍价HTTP控制器
 */
@RestController
@RequestMapping("/app/api/v1/bargain")
@Validated
@Slf4j
public class BargainActivityController {

    private final BargainActivityService bargainActivityService;

    public BargainActivityController(BargainActivityService bargainActivityService) {
        this.bargainActivityService = bargainActivityService;
    }

    /**
     * 发起助力活动
     */
    @PostMapping("/start")
    public ApiResponse<BargainActivityResponse> startBargainActivity(
            @Valid @RequestBody StartBargainActivityRequest request) {
        
        log.info("用户发起助力活动: userId={}, activityId={}, productId={}", 
                request.getUserId(), request.getActivityId(), request.getProductId());
        
        UserBargainActivityBO result = bargainActivityService.startBargainActivity(
                BargainVOConverter.convertToDTO(request));
        
        BargainActivityResponse response = BargainVOConverter.convertToResponse(result);
        
        return ApiResponse.success("发起助力活动成功", response);
    }

    /**
     * 参与助力
     */
    @PostMapping("/assist")
    public ApiResponse<BargainAssistRecordResponse> assistBargain(
            @Valid @RequestBody AssistBargainRequest request) {
        
        log.info("用户参与助力: assistUserId={}, userBargainId={}", 
                request.getAssistUserId(), request.getUserBargainId());
        
        BargainAssistRecordBO result = bargainActivityService.assistBargain(
                BargainVOConverter.convertToDTO(request));
        
        BargainAssistRecordResponse response = BargainVOConverter.convertToResponse(result);
        
        return ApiResponse.success("助力成功", response);
    }

    /**
     * 查询用户助力活动列表
     */
    @GetMapping("/activities")
    public ApiResponse<PageResponse<BargainActivityResponse>> queryUserBargainActivities(
            @Valid QueryBargainActivityRequest request) {
        
        log.info("查询用户助力活动列表: userId={}, status={}, pageNum={}", 
                request.getUserId(), request.getStatus(), request.getPageNum());
        
        PageData<UserBargainActivityBO> pageData = bargainActivityService.queryUserBargainActivities(
                BargainVOConverter.convertToDTO(request));
        
        List<BargainActivityResponse> responseList = pageData.getList().stream()
                .map(BargainVOConverter::convertToResponse)
                .collect(Collectors.toList());
        
        PageResponse<BargainActivityResponse> response = BargainVOConverter.convertToPageResponse(
                pageData, responseList);
        
        return ApiResponse.success(response);
    }

    /**
     * 根据ID查询助力活动详情
     */
    @GetMapping("/activity/{userBargainId}")
    public ApiResponse<BargainActivityResponse> getBargainActivityById(
            @PathVariable @NotNull(message = "助力活动ID不能为空") Long userBargainId,
            @RequestParam(defaultValue = "true") Boolean includeRecords) {
        
        log.info("查询助力活动详情: userBargainId={}, includeRecords={}", 
                userBargainId, includeRecords);
        
        UserBargainActivityBO result = bargainActivityService.getBargainActivityById(
                userBargainId, includeRecords);
        
        BargainActivityResponse response = BargainVOConverter.convertToResponse(result);
        
        return ApiResponse.success(response);
    }

    /**
     * 检查用户是否可以助力
     */
    @GetMapping("/check-assist")
    public ApiResponse<BargainAssistCheckResponse> checkCanAssist(
            @RequestParam @NotNull(message = "助力用户ID不能为空") String assistUserId,
            @RequestParam @NotNull(message = "助力活动ID不能为空") Long userBargainId,
            @RequestParam @NotNull(message = "应用ID不能为空") String appId) {
        
        log.info("检查用户是否可以助力: assistUserId={}, userBargainId={}", 
                assistUserId, userBargainId);
        
        BargainActivityService.BargainAssistCheckResult result = 
                bargainActivityService.checkCanAssist(assistUserId, userBargainId, appId);
        
        BargainAssistCheckResponse response = new BargainAssistCheckResponse();
        response.setCanAssist(result.isCanAssist());
        response.setReason(result.getReason());
        
        return ApiResponse.success(response);
    }

    /**
     * 获取用户今日剩余助力次数
     */
    @GetMapping("/remaining-assist-count")
    public ApiResponse<Integer> getUserDailyRemainingAssistCount(
            @RequestParam @NotNull(message = "用户ID不能为空") String userId,
            @RequestParam @NotNull(message = "应用ID不能为空") String appId) {
        
        log.info("获取用户今日剩余助力次数: userId={}", userId);
        
        Integer remainingCount = bargainActivityService.getUserDailyRemainingAssistCount(userId, appId);
        
        return ApiResponse.success(remainingCount);
    }
}
