package com.blsc.marketing.bargain.bean.vo;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 发起助力活动请求
 */
@Data
public class StartBargainActivityRequest {

    /**
     * 活动配置ID
     */
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    /**
     * 发起用户ID (从请求头获取)
     */
    private String userId;

    /**
     * 应用ID
     */
    @NotBlank(message = "应用ID不能为空")
    private String appId;

    /**
     * 商品ID
     */
    @NotBlank(message = "商品ID不能为空")
    private String productId;
}
