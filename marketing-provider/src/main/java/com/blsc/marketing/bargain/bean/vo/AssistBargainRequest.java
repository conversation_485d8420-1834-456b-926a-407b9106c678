package com.blsc.marketing.bargain.bean.vo;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 参与助力请求
 */
@Data
public class AssistBargainRequest {

    /**
     * 用户助力活动ID
     */
    @NotNull(message = "助力活动ID不能为空")
    private Long userBargainId;

    /**
     * 助力用户ID (从请求头获取)
     */
    private String assistUserId;

    /**
     * 应用ID
     */
    @NotBlank(message = "应用ID不能为空")
    private String appId;
}
