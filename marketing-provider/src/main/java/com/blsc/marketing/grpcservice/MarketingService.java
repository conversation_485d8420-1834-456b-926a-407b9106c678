package com.blsc.marketing.grpcservice;

import com.blsc.marketing.bargain.bean.PageData;
import com.blsc.marketing.bargain.bean.bo.UserBargainActivityBO;
import com.blsc.marketing.bargain.bean.dto.QueryBargainActivityDTO;
import com.blsc.marketing.bargain.service.BargainActivityService;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.service.GrpcService;

@GrpcService
@Slf4j
public class MarketingService extends MarketingServiceGrpc.MarketingServiceImplBase {

    private final BargainActivityService bargainActivityService;

    public MarketingService(BargainActivityService bargainActivityService) {
        this.bargainActivityService = bargainActivityService;
    }

    @Override
    public void calculatePrice(MarketingServiceProto.CalculatePriceRequest request, StreamObserver<MarketingServiceProto.CalculatePriceResponse> responseObserver) {
        // 获取当前用户正在进行的助力活动
        PageData<UserBargainActivityBO> userBargainActivityBOPageData = bargainActivityService
                .queryUserBargainActivities(new QueryBargainActivityDTO().setUserId(request.getUserId()).setStatus(1));
        // 用产品 ID 反查用户在某个活动下唯一正在进行的活动
        userBargainActivityBOPageData.getList().stream().filter(activity -> activity.getProductId().equals(request.getProductId()))
                .findFirst().ifPresentOrElse(activity -> {

                    MarketingServiceProto.CalculatePriceResponse response = MarketingServiceProto.CalculatePriceResponse.newBuilder()
                            .setOriginalAmount(request.getOriginalAmount())
                            .setTotalDiscountAmount(activity.getTotalBargainAmount())
                            .setFinalAmount(activity.getCurrentPrice())
                            .setActivityDiscountAmount(activity.getTotalBargainAmount())
                            .setSuccess(true)
                            .build();

                    responseObserver.onNext(response);
                    responseObserver.onCompleted();
                }, () -> {
                    // 没有找到对应的砍价活动，返回原价
                    MarketingServiceProto.CalculatePriceResponse response = MarketingServiceProto.CalculatePriceResponse.newBuilder()
                            .setOriginalAmount(request.getOriginalAmount())
                            .setTotalDiscountAmount(0)
                            .setFinalAmount(request.getOriginalAmount())
                            .setActivityDiscountAmount(0)
                            .setSuccess(true)
                            .setMessage("没有找到对应的砍价活动")
                            .build();

                    responseObserver.onNext(response);
                    responseObserver.onCompleted();
                });
    }
}
