package com.blsc.marketing.coupon.util;

import com.blsc.marketing.coupon.bean.CouponConstants;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.Set;

/**
 * 优惠券码生成器
 */
public class CouponCodeGenerator {
    
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final SecureRandom RANDOM = new SecureRandom();
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyMMdd");
    
    /**
     * 生成优惠券码
     * 
     * @param templateId 模板ID
     * @return 优惠券码
     */
    public static String generateCouponCode(Long templateId) {
        StringBuilder code = new StringBuilder();
        
        // 添加日期前缀（6位）
        code.append(LocalDateTime.now().format(DATE_FORMATTER));
        
        // 添加模板ID后缀（取后4位，不足补0）
        String templateSuffix = String.format("%04d", templateId % 10000);
        code.append(templateSuffix);
        
        // 添加随机字符（6位）
        for (int i = 0; i < 6; i++) {
            code.append(CHARACTERS.charAt(RANDOM.nextInt(CHARACTERS.length())));
        }
        
        return code.toString();
    }
    
    /**
     * 批量生成优惠券码
     * 
     * @param templateId 模板ID
     * @param count 生成数量
     * @return 优惠券码集合
     */
    public static Set<String> generateCouponCodes(Long templateId, Integer count) {
        Set<String> codes = new HashSet<>();
        int attempts = 0;
        int maxAttempts = count * 3; // 最大尝试次数，避免无限循环
        
        while (codes.size() < count && attempts < maxAttempts) {
            String code = generateCouponCode(templateId);
            codes.add(code);
            attempts++;
        }
        
        if (codes.size() < count) {
            throw new RuntimeException("生成优惠券码失败，可能存在重复");
        }
        
        return codes;
    }
    
    /**
     * 生成指定长度的随机字符串
     * 
     * @param length 长度
     * @return 随机字符串
     */
    public static String generateRandomString(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(CHARACTERS.charAt(RANDOM.nextInt(CHARACTERS.length())));
        }
        return sb.toString();
    }
    
    /**
     * 验证优惠券码格式
     * 
     * @param couponCode 优惠券码
     * @return 是否有效
     */
    public static boolean isValidCouponCode(String couponCode) {
        if (couponCode == null || couponCode.length() != CouponConstants.BusinessLimit.COUPON_CODE_LENGTH) {
            return false;
        }
        
        // 检查是否只包含允许的字符
        for (char c : couponCode.toCharArray()) {
            if (CHARACTERS.indexOf(c) == -1) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 从优惠券码中提取模板ID
     * 
     * @param couponCode 优惠券码
     * @return 模板ID（如果无法提取则返回null）
     */
    public static Long extractTemplateId(String couponCode) {
        if (!isValidCouponCode(couponCode)) {
            return null;
        }
        
        try {
            // 提取第7-10位作为模板ID
            String templateIdStr = couponCode.substring(6, 10);
            return Long.parseLong(templateIdStr);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 从优惠券码中提取日期
     * 
     * @param couponCode 优惠券码
     * @return 日期字符串（如果无法提取则返回null）
     */
    public static String extractDate(String couponCode) {
        if (!isValidCouponCode(couponCode)) {
            return null;
        }
        
        try {
            // 提取前6位作为日期
            return couponCode.substring(0, 6);
        } catch (Exception e) {
            return null;
        }
    }
}
