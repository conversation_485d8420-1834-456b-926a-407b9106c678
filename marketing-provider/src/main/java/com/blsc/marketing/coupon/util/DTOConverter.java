package com.blsc.marketing.coupon.util;

import com.blsc.marketing.coupon.bean.dto.QueryCouponTemplatesDTO;
import com.blsc.marketing.coupon.bean.dto.QueryUsageRecordsDTO;
import com.blsc.marketing.coupon.bean.dto.QueryUserCouponsDTO;
import com.blsc.marketing.coupon.bean.query.CouponTemplatesQuery;
import com.blsc.marketing.coupon.bean.query.UsageRecordsQuery;
import com.blsc.marketing.coupon.bean.query.UserCouponsQuery;

/**
 * DTO转换工具类
 * 用于在Service层和Mapper层之间转换对象
 */
public class DTOConverter {
    
    /**
     * 将QueryCouponTemplatesDTO转换为CouponTemplatesQuery
     */
    public static CouponTemplatesQuery convertToQuery(QueryCouponTemplatesDTO dto) {
        if (dto == null) {
            return null;
        }
        
        CouponTemplatesQuery query = new CouponTemplatesQuery();
        query.setTemplateName(dto.getTemplateName());
        query.setTemplateType(dto.getTemplateType());
        query.setScopeType(dto.getScopeType());
        query.setStatus(dto.getStatus());
        query.setPageNum(dto.getPageNum());
        query.setPageSize(dto.getPageSize());
        
        return query;
    }
    
    /**
     * 将QueryUserCouponsDTO转换为UserCouponsQuery
     */
    public static UserCouponsQuery convertToQuery(QueryUserCouponsDTO dto) {
        if (dto == null) {
            return null;
        }
        
        UserCouponsQuery query = new UserCouponsQuery();
        query.setUserId(dto.getUserId());
        query.setAppId(dto.getAppId());
        query.setStatus(dto.getStatus());
        query.setTemplateType(dto.getTemplateType());
        query.setPageNum(dto.getPageNum());
        query.setPageSize(dto.getPageSize());
        
        return query;
    }
    
    /**
     * 将QueryUsageRecordsDTO转换为UsageRecordsQuery
     */
    public static UsageRecordsQuery convertToQuery(QueryUsageRecordsDTO dto) {
        if (dto == null) {
            return null;
        }
        
        UsageRecordsQuery query = new UsageRecordsQuery();
        query.setUserId(dto.getUserId());
        query.setAppId(dto.getAppId());
        query.setOrderId(dto.getOrderId());
        query.setTemplateId(dto.getTemplateId());
        query.setStartTime(dto.getStartTime());
        query.setEndTime(dto.getEndTime());
        query.setPageNum(dto.getPageNum());
        query.setPageSize(dto.getPageSize());
        
        return query;
    }
}
