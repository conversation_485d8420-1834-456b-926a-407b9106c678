package com.blsc.marketing.coupon.util;

import com.blsc.marketing.coupon.bean.CouponConstants;
import com.blsc.marketing.exception.BizException;
import com.blsc.marketing.exception.MarketingServiceCode;

/**
 * 优惠券异常处理工具类
 */
public class CouponExceptionHelper {
    
    /**
     * 优惠券模板不存在异常
     * 
     * @param templateId 模板ID
     * @return 业务异常
     */
    public static BizException templateNotFound(Long templateId) {
        return new BizException(MarketingServiceCode.COUPON_TEMPLATE_NOT_FOUND,
                String.format("优惠券模板不存在，模板ID：%d", templateId));
    }
    
    /**
     * 优惠券不存在异常
     * 
     * @param couponCode 优惠券码
     * @return 业务异常
     */
    public static BizException couponNotFound(String couponCode) {
        return new BizException(MarketingServiceCode.COUPON_NOT_FOUND, 
                String.format("优惠券不存在，优惠券码：%s", couponCode));
    }
    
    /**
     * 用户优惠券不存在异常
     * 
     * @param userId 用户ID
     * @param couponCode 优惠券码
     * @return 业务异常
     */
    public static BizException userCouponNotFound(String userId, String couponCode) {
        return new BizException(MarketingServiceCode.USER_COUPON_NOT_FOUND, 
                String.format("用户优惠券不存在，用户ID：%s，优惠券码：%s", userId, couponCode));
    }
    
    /**
     * 优惠券模板已禁用异常
     * 
     * @param templateId 模板ID
     * @return 业务异常
     */
    public static BizException templateDisabled(Long templateId) {
        return new BizException(MarketingServiceCode.COUPON_TEMPLATE_DISABLED, 
                String.format("优惠券模板已禁用，模板ID：%d", templateId));
    }
    
    /**
     * 优惠券模板已过期异常
     * 
     * @param templateId 模板ID
     * @return 业务异常
     */
    public static BizException templateExpired(Long templateId) {
        return new BizException(MarketingServiceCode.COUPON_TEMPLATE_EXPIRED, 
                String.format("优惠券模板已过期，模板ID：%d", templateId));
    }
    
    /**
     * 优惠券已使用异常
     * 
     * @param couponCode 优惠券码
     * @return 业务异常
     */
    public static BizException couponAlreadyUsed(String couponCode) {
        return new BizException(MarketingServiceCode.COUPON_ALREADY_USED, 
                String.format("优惠券已使用，优惠券码：%s", couponCode));
    }
    
    /**
     * 优惠券已过期异常
     * 
     * @param couponCode 优惠券码
     * @return 业务异常
     */
    public static BizException couponExpired(String couponCode) {
        return new BizException(MarketingServiceCode.COUPON_EXPIRED, 
                String.format("优惠券已过期，优惠券码：%s", couponCode));
    }
    
    /**
     * 优惠券库存不足异常
     * 
     * @param templateId 模板ID
     * @return 业务异常
     */
    public static BizException insufficientStock(Long templateId) {
        return new BizException(MarketingServiceCode.INSUFFICIENT_COUPON_STOCK, 
                String.format("优惠券库存不足，模板ID：%d", templateId));
    }
    
    /**
     * 超出用户领取限制异常
     * 
     * @param userId 用户ID
     * @param templateId 模板ID
     * @param limit 限制数量
     * @return 业务异常
     */
    public static BizException exceedUserClaimLimit(String userId, Long templateId, Integer limit) {
        return new BizException(MarketingServiceCode.EXCEED_USER_CLAIM_LIMIT, 
                String.format("超出用户领取限制，用户ID：%s，模板ID：%d，限制数量：%d", userId, templateId, limit));
    }
    
    /**
     * 无效的优惠券类型异常
     * 
     * @param couponType 优惠券类型
     * @return 业务异常
     */
    public static BizException invalidCouponType(Integer couponType) {
        return new BizException(MarketingServiceCode.INVALID_COUPON_TYPE, 
                String.format("无效的优惠券类型：%d", couponType));
    }
    
    /**
     * 无效的使用范围类型异常
     * 
     * @param scopeType 使用范围类型
     * @return 业务异常
     */
    public static BizException invalidScopeType(Integer scopeType) {
        return new BizException(MarketingServiceCode.INVALID_SCOPE_TYPE, 
                String.format("无效的使用范围类型：%d", scopeType));
    }
    
    /**
     * 无效的折扣率异常
     * 
     * @param discountRate 折扣率
     * @return 业务异常
     */
    public static BizException invalidDiscountRate(Double discountRate) {
        return new BizException(MarketingServiceCode.INVALID_DISCOUNT_RATE, 
                String.format("无效的折扣率：%f，折扣率应在%.2f-%.2f之间", 
                        discountRate, CouponConstants.BusinessLimit.MIN_DISCOUNT_RATE, 
                        CouponConstants.BusinessLimit.MAX_DISCOUNT_RATE));
    }
    
    /**
     * 订单金额不满足使用条件异常
     * 
     * @param orderAmount 订单金额
     * @param minAmount 最低消费金额
     * @return 业务异常
     */
    public static BizException orderAmountNotMeet(Integer orderAmount, Integer minAmount) {
        return new BizException(MarketingServiceCode.ORDER_AMOUNT_NOT_MEET, 
                String.format("订单金额不满足使用条件，订单金额：%d分，最低消费金额：%d分", orderAmount, minAmount));
    }
    
    /**
     * 优惠券使用范围不匹配异常
     * 
     * @param couponCode 优惠券码
     * @param appId 应用ID
     * @return 业务异常
     */
    public static BizException couponScopeNotMatch(String couponCode, String appId) {
        return new BizException(MarketingServiceCode.COUPON_SCOPE_NOT_MATCH, 
                String.format("优惠券使用范围不匹配，优惠券码：%s，应用ID：%s", couponCode, appId));
    }
    
    /**
     * 通用参数错误异常
     * 
     * @param message 错误信息
     * @return 业务异常
     */
    public static BizException invalidArgument(String message) {
        return new BizException(MarketingServiceCode.INVALID_ARGUMENT, message);
    }
    
    /**
     * 通用业务前置条件错误异常
     * 
     * @param message 错误信息
     * @return 业务异常
     */
    public static BizException failedPrecondition(String message) {
        return new BizException(MarketingServiceCode.FAILED_PRECONDITION, message);
    }
    
    /**
     * 通用内部错误异常
     * 
     * @param message 错误信息
     * @return 业务异常
     */
    public static BizException internalError(String message) {
        return new BizException(MarketingServiceCode.INTERNAL_ERROR, message);
    }
    
    /**
     * 通用内部错误异常（带异常堆栈）
     *
     * @param message 错误信息
     * @param cause 原始异常
     * @return 业务异常
     */
    public static BizException internalError(String message, Throwable cause) {
        return new BizException(MarketingServiceCode.INTERNAL_ERROR, message, cause);
    }

    // 以下是Service实现中需要的异常方法

    public static BizException createTemplateFailedException(String message) {
        return new BizException(MarketingServiceCode.INTERNAL_ERROR, "创建模板失败：" + message);
    }

    public static BizException invalidParameterException(String message) {
        return new BizException(MarketingServiceCode.INVALID_ARGUMENT, message);
    }

    public static BizException templateNotFoundException(Long templateId) {
        return templateNotFound(templateId);
    }

    public static BizException queryTemplateFailedException(String message) {
        return new BizException(MarketingServiceCode.INTERNAL_ERROR, "查询模板失败：" + message);
    }

    public static BizException updateTemplateFailedException(String message) {
        return new BizException(MarketingServiceCode.INTERNAL_ERROR, "更新模板失败：" + message);
    }

    public static BizException deleteTemplateFailedException(String message) {
        return new BizException(MarketingServiceCode.INTERNAL_ERROR, "删除模板失败：" + message);
    }

    public static BizException templateCannotDeleteException(String message) {
        return new BizException(MarketingServiceCode.FAILED_PRECONDITION, message);
    }

    public static BizException claimCouponFailedException(String message) {
        return new BizException(MarketingServiceCode.INTERNAL_ERROR, "领取优惠券失败：" + message);
    }

    public static BizException queryCouponFailedException(String message) {
        return new BizException(MarketingServiceCode.INTERNAL_ERROR, "查询优惠券失败：" + message);
    }

    public static BizException couponNotFoundException(String couponCode) {
        return couponNotFound(couponCode);
    }

    public static BizException validateCouponFailedException(String message) {
        return new BizException(MarketingServiceCode.INTERNAL_ERROR, "验证优惠券失败：" + message);
    }

    public static BizException useCouponFailedException(String message) {
        return new BizException(MarketingServiceCode.INTERNAL_ERROR, "使用优惠券失败：" + message);
    }

    public static BizException queryUsageRecordFailedException(String message) {
        return new BizException(MarketingServiceCode.INTERNAL_ERROR, "查询使用记录失败：" + message);
    }

    public static BizException expireCouponFailedException(String message) {
        return new BizException(MarketingServiceCode.INTERNAL_ERROR, "过期优惠券失败：" + message);
    }

    public static BizException revokeCouponFailedException(String message) {
        return new BizException(MarketingServiceCode.INTERNAL_ERROR, "撤销优惠券失败：" + message);
    }
}
