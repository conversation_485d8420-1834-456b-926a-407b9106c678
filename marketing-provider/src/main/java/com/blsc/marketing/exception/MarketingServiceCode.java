package com.blsc.marketing.exception;

import io.grpc.Status;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName MarketingServiceCode
 * @Description 营销业务状态码枚举类，业务交互的状态前缀与 gRPC 协议中定义的状态码一致，后缀用于同类状态下的具体状态。非业务状态根据场景定义。
 *              格式规范见<a href="https://blsc.coding.net/p/system/km/spaces/3082400/pages/K-12">文档</a>
 * <AUTHOR>
 * @Date 2024/10/19 16:58
 */
@Getter
@AllArgsConstructor
public enum MarketingServiceCode {

    /**
     * NOT_FIND 相关业务码，从 05101 到 05110
     * @see Status#NOT_FOUND
     */
    NOT_FIND("05101"),
    NOT_FIND_PLACEHOLDER("05110"),  // 占位，以控制状态码的范围

    /**
     * FAILED_PRECONDITION 状态转移错误，代表调用时系统内的状态不符合能够成功转移的前置条件，从 05121 到 05130
     * @see Status#FAILED_PRECONDITION
     */
    FAILED_PRECONDITION("05121"),
    FAILED_PRECONDITION_PLACEHOLDER("05130"),

    /**
     * INVALID_ARGUMENT 输入参数错误，代表调用时传入的参数不符合要求，从 05131 到 05140
     * @see Status#INVALID_ARGUMENT
     */
    INVALID_ARGUMENT("05131"),
    INVALID_ARGUMENT_PLACEHOLDER("05140"),

    /**
     * INTERNAL_ERROR 服务内部业务异常，从 05201 到 05299
     * @see Status#INTERNAL
     */
    INTERNAL_ERROR("05201"),
    INTERNAL_ERROR_PLACEHOLDER("05299"),

    /**
     *  OUT_SERVICE_ERROR 访问外部系统出错，从 05301 到 05399
     */
    OUT_SERVICE_ERROR("05301"),
    OUT_SERVICE_PLACEHOLDER("05399"),
    ;


    private final String value;

    public int getErrorCodeInt() {
        return Integer.parseInt(value);
    }
}
