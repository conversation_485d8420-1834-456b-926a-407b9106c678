package com.blsc.marketing.exception;

import com.blsc.marketing.bargain.bean.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.stream.Collectors;

/**
 * HTTP接口全局异常处理器
 */
@RestControllerAdvice
@Slf4j
public class HttpExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler
    public ResponseEntity<JsonResult<Void>> handleBizException(BizException e) {
        log.error("[handleBizException] {}", e.getMessage(), e);
        return ResponseEntity.status(convertToHttpStatus(e)).body(JsonResult.fail(e.getErrorCodeInt(), e.getMessage()));
    }

    /**
     * 处理参数校验异常 - @RequestBody 参数校验
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public JsonResult<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        log.warn("参数校验失败: {}", message, e);
        return JsonResult.fail("参数校验失败");
    }

    /**
     * 处理参数校验异常 - @ModelAttribute 参数校验
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public JsonResult<Void> handleBindException(BindException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        log.warn("参数绑定失败: {}", message, e);
        return JsonResult.fail("参数绑定失败");
    }

    /**
     * 处理参数校验异常 - @RequestParam 和 @PathVariable 参数校验
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public JsonResult<Void> handleConstraintViolationException(ConstraintViolationException e) {
        String message = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        log.warn("约束校验失败: {}", message, e);
        return JsonResult.fail("约束校验失败");
    }

    /**
     * 处理参数类型转换异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public JsonResult<Void> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("参数错误: {}", e.getMessage(), e);
        return JsonResult.fail("参数错误");
    }

    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public JsonResult<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return JsonResult.fail("服务器异常, 请联系管理员");
    }

    private HttpStatus convertToHttpStatus(BizException e) {
        int errorCode = e.getErrorCodeInt();

        if (errorCode >= MarketingServiceCode.NOT_FIND.getErrorCodeInt()
                && errorCode <= MarketingServiceCode.NOT_FIND_PLACEHOLDER.getErrorCodeInt()) {
            return HttpStatus.NOT_FOUND;
        } else if (errorCode >= MarketingServiceCode.FAILED_PRECONDITION.getErrorCodeInt()
                && errorCode <= MarketingServiceCode.FAILED_PRECONDITION_PLACEHOLDER.getErrorCodeInt()) {
            return HttpStatus.CONFLICT;
        } else if (errorCode >= MarketingServiceCode.INVALID_ARGUMENT.getErrorCodeInt()
                && errorCode <= MarketingServiceCode.INVALID_ARGUMENT_PLACEHOLDER.getErrorCodeInt()) {
            return HttpStatus.BAD_REQUEST;
        } else if (errorCode >= MarketingServiceCode.INTERNAL_ERROR.getErrorCodeInt()
                && errorCode <= MarketingServiceCode.INTERNAL_ERROR_PLACEHOLDER.getErrorCodeInt()) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        } else {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }
    }

}
