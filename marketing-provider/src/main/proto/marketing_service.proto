syntax = "proto3";

package com.blsc.marketing;

option java_package = "com.blsc.marketing.grpcservice";
option java_outer_classname = "MarketingServiceProto";

// 营销服务
service MarketingService {
  // 计算营销价格
  rpc CalculatePrice(CalculatePriceRequest) returns (CalculatePriceResponse);
}

// ==================== 营销价格计算相关 ====================

// 计算营销价格请求
message CalculatePriceRequest {
  string user_id = 1;
  string app_id = 2;
  int32 original_amount = 3;  // 原始金额(分)
  repeated string coupon_ids = 4;
  string product_id = 5;
}

// 计算营销价格响应
message CalculatePriceResponse {
  int32 original_amount = 1;  // 原始金额(分)
  int32 total_discount_amount = 2;  // 总优惠金额(分)
  int32 final_amount = 3;  // 最终金额(分)
  repeated CouponDiscountDetail coupon_details = 4;
  int32 activity_discount_amount = 5;  // 活动优惠金额(分)
  bool success = 6;
  string message = 7;
}

// 优惠券优惠详情
message CouponDiscountDetail {
  string coupon_code = 1;
  int64 template_id = 2;
  string template_name = 3;
  int32 template_type = 4;
  int32 discount_amount = 5;
  bool used = 6;
  string fail_reason = 7;
}
